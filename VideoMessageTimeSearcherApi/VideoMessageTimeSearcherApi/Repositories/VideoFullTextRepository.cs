using Microsoft.EntityFrameworkCore;
using System.Text.RegularExpressions;
using VideoMessageTimeSearcherApi.DbContexts;
using VideoMessageTimeSearcherApi.DTOs;
using VideoMessageTimeSearcherApi.Models;
using VideoMessageTimeSearcherApi.Repositories.Interfaces;

namespace VideoMessageTimeSearcherApi.Repositories
{
    public class VideoFullTextRepository : IVideoFullTextRepository
    {
        private readonly AppDbContext _context;

        public VideoFullTextRepository(AppDbContext context)
        {
            _context = context;
        }

        public async Task<VideoFullText> GetByIdAsync(int id)
        {
            return await _context.VideoFullTexts
                .Include(vft => vft.Video)
                .FirstOrDefaultAsync(vft => vft.Id == id);
        }

        public async Task<VideoFullText> GetByVideoIdAsync(int videoId)
        {
            return await _context.VideoFullTexts
                .Include(vft => vft.Video)
                .FirstOrDefaultAsync(vft => vft.VideoId == videoId);
        }

        public async Task<VideoFullText> CreateAsync(VideoFullText videoFullText)
        {
            videoFullText.CreatedAt = DateTime.UtcNow;
            videoFullText.UpdatedAt = DateTime.UtcNow;
            
            _context.VideoFullTexts.Add(videoFullText);
            await _context.SaveChangesAsync();
            return videoFullText;
        }

        public async Task<bool> UpdateAsync(VideoFullText videoFullText)
        {
            videoFullText.UpdatedAt = DateTime.UtcNow;
            _context.Entry(videoFullText).State = EntityState.Modified;
            return await _context.SaveChangesAsync() > 0;
        }

        public async Task<bool> DeleteAsync(int id)
        {
            var videoFullText = await _context.VideoFullTexts.FindAsync(id);
            if (videoFullText == null) return false;

            _context.VideoFullTexts.Remove(videoFullText);
            return await _context.SaveChangesAsync() > 0;
        }

        public async Task<bool> DeleteByVideoIdAsync(int videoId)
        {
            var videoFullText = await _context.VideoFullTexts
                .FirstOrDefaultAsync(vft => vft.VideoId == videoId);
            
            if (videoFullText == null) return false;

            _context.VideoFullTexts.Remove(videoFullText);
            return await _context.SaveChangesAsync() > 0;
        }

        public async Task<bool> ExistsForVideoAsync(int videoId)
        {
            return await _context.VideoFullTexts
                .AnyAsync(vft => vft.VideoId == videoId);
        }

        public async Task<PagedResult<VideoFullTextSearchResult>> SearchAsync(VideoFullTextSearchRequest request)
        {
            var query = _context.VideoFullTexts
                .Include(vft => vft.Video)
                .AsQueryable();

            var results = new List<VideoFullTextSearchResult>();

            if (!string.IsNullOrWhiteSpace(request.SearchText))
            {
                var videoFullTexts = await query.ToListAsync();

                // Prepare search pattern
                var searchPattern = request.MatchWholeWord 
                    ? $"\\b{Regex.Escape(request.SearchText)}\\b" 
                    : Regex.Escape(request.SearchText);

                var regexOptions = request.MatchCase ? RegexOptions.None : RegexOptions.IgnoreCase;
                var regex = new Regex(searchPattern, regexOptions);

                foreach (var videoFullText in videoFullTexts)
                {
                    var matches = regex.Matches(videoFullText.FullText);
                    
                    foreach (Match match in matches)
                    {
                        var contextLength = 100; // Characters before and after the match
                        var startPos = Math.Max(0, match.Index - contextLength);
                        var endPos = Math.Min(videoFullText.FullText.Length, match.Index + match.Length + contextLength);
                        
                        var contextBefore = videoFullText.FullText.Substring(startPos, match.Index - startPos);
                        var contextAfter = videoFullText.FullText.Substring(match.Index + match.Length, 
                            endPos - (match.Index + match.Length));

                        results.Add(new VideoFullTextSearchResult
                        {
                            VideoId = videoFullText.VideoId,
                            VideoTitle = videoFullText.Video?.Title ?? "",
                            MatchedText = match.Value,
                            MatchPosition = match.Index,
                            ContextBefore = contextBefore,
                            ContextAfter = contextAfter
                        });
                    }
                }
            }

            // Apply pagination
            var totalCount = results.Count;
            var pagedResults = results
                .Skip((request.PageNumber - 1) * request.PageSize)
                .Take(request.PageSize)
                .ToList();

            return new PagedResult<VideoFullTextSearchResult>
            {
                Items = pagedResults,
                TotalCount = totalCount,
                PageNumber = request.PageNumber,
                PageSize = request.PageSize
            };
        }
    }
}
