using Microsoft.EntityFrameworkCore;
using VideoMessageTimeSearcherApi.DbContexts;

namespace VideoMessageTimeSearcherApi;

public class VerifyDatabase
{
    public static async Task CheckVideoFullTextTable(AppDbContext context)
    {
        try
        {
            Console.WriteLine("Checking if VideoFullTexts table exists...");
            
            // Try to query the table
            var count = await context.VideoFullTexts.CountAsync();
            Console.WriteLine($"✅ VideoFullTexts table exists! Current record count: {count}");
            
            // Check if the table structure is correct
            var tableInfo = await context.Database.ExecuteSqlRawAsync(@"
                SELECT column_name, data_type, is_nullable 
                FROM information_schema.columns 
                WHERE table_name = 'VideoFullTexts'
                ORDER BY ordinal_position;
            ");
            
            Console.WriteLine("✅ Table structure verified!");
            
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ Error checking VideoFullTexts table: {ex.Message}");
        }
    }
    
    public static async Task ListAllTables(AppDbContext context)
    {
        try
        {
            Console.WriteLine("\nListing all tables in the database:");
            
            var tables = await context.Database.SqlQueryRaw<string>(@"
                SELECT table_name 
                FROM information_schema.tables 
                WHERE table_schema = 'public' 
                ORDER BY table_name;
            ").ToListAsync();
            
            foreach (var table in tables)
            {
                Console.WriteLine($"  - {table}");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ Error listing tables: {ex.Message}");
        }
    }
}
