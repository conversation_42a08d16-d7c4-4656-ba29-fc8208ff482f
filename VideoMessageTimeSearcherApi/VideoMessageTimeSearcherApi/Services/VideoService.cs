using AutoMapper;
using VideoMessageTimeSearcherApi.DTOs;
using VideoMessageTimeSearcherApi.Models;
using VideoMessageTimeSearcherApi.Repositories.Interfaces;
using VideoMessageTimeSearcherApi.Services.Interfaces;

namespace VideoMessageTimeSearcherApi.Services
{
    public class VideoService : IVideoService
    {
        private readonly IVideoRepository _videoRepository;
        private readonly IMapper _mapper;

        public VideoService(IVideoRepository videoRepository, IMapper mapper)
        {
            _videoRepository = videoRepository;
            _mapper = mapper;
        }

        public async Task<PagedResult<VideoDto>> SearchVideosAsync(SearchVideosRequest request)
        {
            // Validate request
            if (request.PageNumber < 1) request.PageNumber = 1;
            if (request.PageSize < 1 || request.PageSize > 100) request.PageSize = 10;

            // Call repository
            var result = await _videoRepository.SearchVideosAsync(request);

            // Map to DTOs
            var mappedItems = _mapper.Map<List<VideoDto>>(result.Items);

            return new PagedResult<VideoDto>
            {
                Items = mappedItems,
                TotalCount = result.TotalCount,
                PageNumber = result.PageNumber,
                PageSize = result.PageSize
            };
        }


        public async Task<List<WordOccurrenceDto>> SearchTranscriptsAsync(SearchTranscriptRequest request)
        {
            if (string.IsNullOrWhiteSpace(request.SearchText))
            {
                return new List<WordOccurrenceDto>();
            }

            // Trim and normalize the search text
            request.SearchText = request.SearchText.Trim();

            return await _videoRepository.SearchTranscriptsAsync(request);
        }

        #region Video Operations

        public async Task<VideoDto> GetVideoByIdAsync(int id)
        {
            var video = await _videoRepository.GetVideoByIdAsync(id);
            return video == null ? null : _mapper.Map<VideoDto>(video);
        }

        public async Task<VideoDto> CreateVideoAsync(CreateVideoRequest request)
        {
            // Validate transcript segments timing
            ValidateTranscriptSegments(request);

            var video = _mapper.Map<Video>(request);
            var createdVideo = await _videoRepository.CreateVideoAsync(video);

            // Create transcript segment groups if provided
            if (request.TranscriptSegments != null && request.TranscriptSegments.Any())
            {
                foreach (var segmentRequest in request.TranscriptSegments)
                {
                    var segmentGroup = new TranscriptSegmentGroup
                    {
                        VideoId = createdVideo.Id,
                        StartTime = segmentRequest.StartTime,
                        EndTime = segmentRequest.EndTime
                    };

                    var createdSegmentGroup = await _videoRepository.CreateSegmentGroupAsync(segmentGroup);

                    // Create a transcript segment for the text (assuming English for now)
                    var segment = new TranscriptSegment
                    {
                        GroupId = createdSegmentGroup.Id,
                        LanguageCode = "en", // Default to English, could be made configurable
                        Text = segmentRequest.Text
                    };

                    var createdSegment = await _videoRepository.CreateSegmentAsync(segment);

                    // Create word timings if provided
                    if (segmentRequest.Words != null && segmentRequest.Words.Any())
                    {
                        var wordIndex = 0;
                        foreach (var wordData in segmentRequest.Words)
                        {
                            var wordTiming = new WordTiming
                            {
                                SegmentId = createdSegment.Id,
                                Word = wordData.Word,
                                StartTime = wordData.StartTime,
                                EndTime = wordData.EndTime,
                                WordIndex = wordIndex++
                            };

                            await _videoRepository.CreateWordTimingAsync(wordTiming);
                        }
                    }
                }
            }

            return _mapper.Map<VideoDto>(createdVideo);
        }

        public async Task<bool> UpdateVideoAsync(int id, CreateVideoRequest request)
        {
            var video = await _videoRepository.GetVideoByIdAsync(id);
            if (video == null) return false;

            _mapper.Map(request, video);
            var updateResult = await _videoRepository.UpdateVideoAsync(video);

            return updateResult;
        }

        public async Task<bool> DeleteVideoAsync(int id)
        {
            return await _videoRepository.DeleteVideoAsync(id);
        }

        #endregion

        #region Private Helper Methods

        private void ValidateTranscriptSegments(CreateVideoRequest request)
        {
            if (request.TranscriptSegments == null || !request.TranscriptSegments.Any())
                return;

            // Sort segments by start time for validation
            var sortedSegments = request.TranscriptSegments.OrderBy(s => s.StartTime).ToList();

            for (int i = 0; i < sortedSegments.Count; i++)
            {
                var segment = sortedSegments[i];

                // Validate segment timing
                if (segment.EndTime <= segment.StartTime)
                {
                    throw new ArgumentException($"Segment {i + 1}: End time must be greater than start time");
                }

                if (segment.StartTime < 0)
                {
                    throw new ArgumentException($"Segment {i + 1}: Start time cannot be negative");
                }

                if (segment.EndTime > request.Duration)
                {
                    throw new ArgumentException($"Segment {i + 1}: End time cannot exceed video duration");
                }

                // Check for overlapping segments
                if (i > 0 && segment.StartTime < sortedSegments[i - 1].EndTime)
                {
                    throw new ArgumentException($"Segment {i + 1}: Overlaps with previous segment");
                }

                // Validate word timings if present
                if (segment.Words != null && segment.Words.Any())
                {
                    var sortedWords = segment.Words.OrderBy(w => w.StartTime).ToList();

                    for (int j = 0; j < sortedWords.Count; j++)
                    {
                        var word = sortedWords[j];

                        // Validate word timing
                        if (word.EndTime <= word.StartTime)
                        {
                            throw new ArgumentException($"Segment {i + 1}, Word {j + 1}: End time must be greater than start time");
                        }

                        if (word.StartTime < segment.StartTime)
                        {
                            throw new ArgumentException($"Segment {i + 1}, Word {j + 1}: Word start time cannot be before segment start time");
                        }

                        if (word.EndTime > segment.EndTime)
                        {
                            throw new ArgumentException($"Segment {i + 1}, Word {j + 1}: Word end time cannot be after segment end time");
                        }

                        // Check for overlapping words
                        if (j > 0 && word.StartTime < sortedWords[j - 1].EndTime)
                        {
                            throw new ArgumentException($"Segment {i + 1}, Word {j + 1}: Overlaps with previous word");
                        }
                    }
                }
            }
        }

        #endregion

    }
}
